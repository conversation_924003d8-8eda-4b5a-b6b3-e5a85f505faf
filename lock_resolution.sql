-- =====================================================
-- Oracle 锁表问题诊断和解决脚本
-- =====================================================

-- 1. 详细的锁链分析
-- =====================================================
PROMPT ========== 锁链分析 ==========

-- 显示完整的锁等待链
WITH lock_tree AS (
    SELECT 
        level as 锁链层级,
        LPAD(' ', (level-1)*2) || s.sid as 会话层次,
        s.sid,
        s.serial#,
        s.username,
        s.program,
        s.machine,
        s.blocking_session,
        s.seconds_in_wait,
        s.event,
        CONNECT_BY_ROOT s.sid as 根阻塞会话
    FROM v$session s
    WHERE s.username IS NOT NULL
    START WITH s.blocking_session IS NULL 
        AND s.sid IN (SELECT blocking_session FROM v$session WHERE blocking_session IS NOT NULL)
    CONNECT BY PRIOR s.sid = s.blocking_session
)
SELECT 
    锁链层级,
    会话层次,
    sid as 会话ID,
    serial# as 序列号,
    username as 用户名,
    program as 程序,
    machine as 机器,
    blocking_session as 阻塞会话,
    seconds_in_wait as 等待秒数,
    event as 等待事件,
    根阻塞会话
FROM lock_tree
ORDER BY 根阻塞会话, 锁链层级;

-- 2. 锁定对象的详细信息
-- =====================================================
PROMPT ========== 被锁定的对象详情 ==========

SELECT 
    o.owner as 对象所有者,
    o.object_name as 对象名,
    o.object_type as 对象类型,
    l.sid as 会话ID,
    s.username as 用户名,
    s.program as 程序,
    DECODE(l.lmode,
        0, 'None',
        1, 'Null',
        2, 'Row-S (SS)',
        3, 'Row-X (SX)',
        4, 'Share',
        5, 'S/Row-X (SSX)',
        6, 'Exclusive',
        TO_CHAR(l.lmode)) as 持有锁模式,
    DECODE(l.request,
        0, 'None',
        1, 'Null',
        2, 'Row-S (SS)',
        3, 'Row-X (SX)',
        4, 'Share',
        5, 'S/Row-X (SSX)',
        6, 'Exclusive',
        TO_CHAR(l.request)) as 请求锁模式,
    l.block as 是否阻塞,
    s.status as 会话状态,
    s.last_call_et as 最后调用秒数
FROM v$lock l
JOIN v$session s ON l.sid = s.sid
JOIN dba_objects o ON l.id1 = o.object_id
WHERE l.type = 'TM'  -- 表锁
  AND (l.lmode > 0 OR l.request > 0)
  AND s.username IS NOT NULL
ORDER BY o.object_name, l.block DESC, l.lmode DESC;

-- 3. 当前执行的SQL语句
-- =====================================================
PROMPT ========== 阻塞会话正在执行的SQL ==========

SELECT 
    s.sid,
    s.serial#,
    s.username as 用户名,
    s.status as 状态,
    sq.sql_text as 当前SQL语句,
    s.event as 等待事件,
    s.seconds_in_wait as 等待秒数,
    CASE 
        WHEN s.blocking_session IS NULL AND EXISTS (
            SELECT 1 FROM v$session s2 WHERE s2.blocking_session = s.sid
        ) THEN '阻塞源头'
        WHEN s.blocking_session IS NOT NULL THEN '被阻塞'
        ELSE '正常'
    END as 会话类型
FROM v$session s
LEFT JOIN v$sql sq ON s.sql_id = sq.sql_id
WHERE s.username IS NOT NULL
  AND (s.blocking_session IS NOT NULL 
       OR EXISTS (SELECT 1 FROM v$session s2 WHERE s2.blocking_session = s.sid)
       OR s.event LIKE '%lock%')
ORDER BY 
    CASE 
        WHEN s.blocking_session IS NULL AND EXISTS (
            SELECT 1 FROM v$session s2 WHERE s2.blocking_session = s.sid
        ) THEN 1
        WHEN s.blocking_session IS NOT NULL THEN 2
        ELSE 3
    END,
    s.seconds_in_wait DESC;

-- 4. 生成解决锁问题的SQL命令
-- =====================================================
PROMPT ========== 解决锁问题的建议命令 ==========

-- 生成KILL SESSION命令（针对阻塞源头）
SELECT 
    '-- 终止阻塞会话 ' || s.sid || ' (用户: ' || s.username || ', 程序: ' || s.program || ')' as 说明,
    'ALTER SYSTEM KILL SESSION ''' || s.sid || ',' || s.serial# || ''' IMMEDIATE;' as 终止命令,
    '-- 被阻塞的会话数: ' || (
        SELECT COUNT(*) FROM v$session s2 WHERE s2.blocking_session = s.sid
    ) as 影响范围
FROM v$session s
WHERE s.username IS NOT NULL
  AND s.blocking_session IS NULL 
  AND EXISTS (SELECT 1 FROM v$session s2 WHERE s2.blocking_session = s.sid)
ORDER BY (SELECT COUNT(*) FROM v$session s2 WHERE s2.blocking_session = s.sid) DESC;

-- 5. 锁等待统计
-- =====================================================
PROMPT ========== 锁等待时间统计 ==========

SELECT 
    s.event as 等待事件,
    COUNT(*) as 等待会话数,
    AVG(s.seconds_in_wait) as 平均等待秒数,
    MAX(s.seconds_in_wait) as 最大等待秒数,
    MIN(s.seconds_in_wait) as 最小等待秒数
FROM v$session s
WHERE s.event LIKE '%lock%'
  AND s.username IS NOT NULL
GROUP BY s.event
ORDER BY COUNT(*) DESC;

-- 6. 事务信息
-- =====================================================
PROMPT ========== 长时间运行的事务 ==========

SELECT 
    s.sid,
    s.serial#,
    s.username as 用户名,
    s.program as 程序,
    t.start_time as 事务开始时间,
    ROUND((SYSDATE - t.start_date) * 24 * 60, 1) as 运行分钟数,
    t.status as 事务状态,
    t.used_ublk as 使用的回滚块数,
    t.used_urec as 使用的回滚记录数,
    CASE 
        WHEN s.blocking_session IS NULL AND EXISTS (
            SELECT 1 FROM v$session s2 WHERE s2.blocking_session = s.sid
        ) THEN '可能是阻塞源'
        WHEN s.blocking_session IS NOT NULL THEN '被阻塞中'
        ELSE '正常事务'
    END as 事务类型
FROM v$session s
JOIN v$transaction t ON s.saddr = t.ses_addr
WHERE s.username IS NOT NULL
ORDER BY t.start_date;

-- 7. 资源使用情况
-- =====================================================
PROMPT ========== 数据库资源使用情况 ==========

SELECT 
    '资源使用统计' as 统计项,
    (SELECT COUNT(*) FROM v$session) as 当前会话数,
    (SELECT value FROM v$parameter WHERE name = 'sessions') as 最大会话数,
    (SELECT COUNT(*) FROM v$process) as 当前进程数,
    (SELECT value FROM v$parameter WHERE name = 'processes') as 最大进程数,
    (SELECT COUNT(*) FROM v$lock) as 当前锁数量,
    (SELECT COUNT(*) FROM v$transaction) as 当前事务数
FROM dual;

PROMPT ========== 锁问题诊断完成 ==========
PROMPT 
PROMPT 使用建议：
PROMPT 1. 查看锁链分析，找到根源阻塞会话
PROMPT 2. 检查被锁定对象，确认影响范围  
PROMPT 3. 查看阻塞会话的SQL，分析原因
PROMPT 4. 如需终止会话，请谨慎使用生成的KILL SESSION命令
PROMPT 5. 建议先联系相关用户确认是否可以终止
PROMPT
