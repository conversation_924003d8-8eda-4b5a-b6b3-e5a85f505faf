-- =====================================================
-- Oracle 数据库健康监控脚本
-- 可用于定期检查数据库连接和锁状态
-- =====================================================

-- 设置输出格式
SET PAGESIZE 1000
SET LINESIZE 200
SET FEEDBACK OFF
SET HEADING ON

-- 生成报告头
PROMPT =====================================================
SELECT '数据库健康监控报告 - ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') as 报告时间 FROM dual;
PROMPT =====================================================

-- 1. 系统基本信息
-- =====================================================
PROMPT 
PROMPT ========== 系统基本信息 ==========

SELECT 
    (SELECT instance_name FROM v$instance) as 实例名,
    (SELECT host_name FROM v$instance) as 主机名,
    (SELECT version FROM v$instance) as 数据库版本,
    (SELECT status FROM v$instance) as 实例状态,
    (SELECT startup_time FROM v$instance) as 启动时间,
    ROUND((SYSDATE - (SELECT startup_time FROM v$instance)) * 24, 1) as 运行小时数
FROM dual;

-- 2. 连接数监控（关键指标）
-- =====================================================
PROMPT 
PROMPT ========== 连接数监控 ==========

SELECT 
    '连接数检查' as 检查项,
    (SELECT COUNT(*) FROM v$session) as 当前会话数,
    (SELECT COUNT(*) FROM v$session WHERE status = 'ACTIVE') as 活跃会话数,
    (SELECT value FROM v$parameter WHERE name = 'sessions') as 最大会话限制,
    ROUND((SELECT COUNT(*) FROM v$session) / (SELECT value FROM v$parameter WHERE name = 'sessions') * 100, 1) as 使用率百分比,
    CASE 
        WHEN (SELECT COUNT(*) FROM v$session) / (SELECT value FROM v$parameter WHERE name = 'sessions') > 0.9 
        THEN '危险'
        WHEN (SELECT COUNT(*) FROM v$session) / (SELECT value FROM v$parameter WHERE name = 'sessions') > 0.8 
        THEN '警告'
        ELSE '正常'
    END as 状态评估
FROM dual;

-- 连接数趋势（按用户）
SELECT 
    username as 用户名,
    COUNT(*) as 连接数,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as 活跃数,
    ROUND(AVG(last_call_et/60), 1) as 平均空闲分钟,
    MAX(last_call_et/60) as 最大空闲分钟
FROM v$session 
WHERE username IS NOT NULL
GROUP BY username
HAVING COUNT(*) >= 3  -- 只显示连接数>=3的用户
ORDER BY COUNT(*) DESC;

-- 3. 锁状态监控（关键指标）
-- =====================================================
PROMPT 
PROMPT ========== 锁状态监控 ==========

SELECT 
    '锁状态检查' as 检查项,
    (SELECT COUNT(*) FROM v$lock WHERE type IN ('TM', 'TX')) as 总锁数,
    (SELECT COUNT(*) FROM v$lock WHERE block = 1) as 阻塞锁数,
    (SELECT COUNT(*) FROM v$session WHERE blocking_session IS NOT NULL) as 被阻塞会话数,
    (SELECT COUNT(DISTINCT blocking_session) FROM v$session WHERE blocking_session IS NOT NULL) as 阻塞源会话数,
    CASE 
        WHEN (SELECT COUNT(*) FROM v$lock WHERE block = 1) > 5 THEN '严重'
        WHEN (SELECT COUNT(*) FROM v$lock WHERE block = 1) > 0 THEN '警告'
        ELSE '正常'
    END as 状态评估
FROM dual;

-- 4. 当前阻塞情况（如果存在）
-- =====================================================
PROMPT 
PROMPT ========== 当前阻塞情况 ==========

-- 阻塞源头会话
SELECT 
    '阻塞源头' as 类型,
    s.sid,
    s.serial#,
    s.username as 用户名,
    s.program as 程序,
    s.machine as 机器,
    (SELECT COUNT(*) FROM v$session WHERE blocking_session = s.sid) as 阻塞会话数,
    ROUND((SYSDATE - s.logon_time) * 24, 1) as 连接小时数,
    s.status as 状态
FROM v$session s
WHERE s.blocking_session IS NULL 
  AND EXISTS (SELECT 1 FROM v$session s2 WHERE s2.blocking_session = s.sid)
ORDER BY (SELECT COUNT(*) FROM v$session WHERE blocking_session = s.sid) DESC;

-- 被阻塞会话（显示等待时间最长的前10个）
SELECT 
    '被阻塞会话' as 类型,
    s.sid,
    s.username as 用户名,
    s.program as 程序,
    s.blocking_session as 阻塞源,
    ROUND(s.seconds_in_wait/60, 1) as 等待分钟数,
    s.event as 等待事件
FROM v$session s
WHERE s.blocking_session IS NOT NULL
ORDER BY s.seconds_in_wait DESC
FETCH FIRST 10 ROWS ONLY;

-- 5. 长时间运行的事务
-- =====================================================
PROMPT 
PROMPT ========== 长时间运行事务 ==========

SELECT 
    s.sid,
    s.username as 用户名,
    s.program as 程序,
    ROUND((SYSDATE - t.start_date) * 24 * 60, 1) as 运行分钟数,
    t.status as 事务状态,
    t.used_ublk as 回滚块数,
    CASE 
        WHEN EXISTS (SELECT 1 FROM v$session s2 WHERE s2.blocking_session = s.sid) THEN '阻塞其他会话'
        WHEN s.blocking_session IS NOT NULL THEN '被阻塞'
        ELSE '正常'
    END as 影响状态
FROM v$session s
JOIN v$transaction t ON s.saddr = t.ses_addr
WHERE (SYSDATE - t.start_date) * 24 * 60 > 30  -- 超过30分钟
ORDER BY t.start_date;

-- 6. 异常会话检测
-- =====================================================
PROMPT 
PROMPT ========== 异常会话检测 ==========

SELECT 
    s.sid,
    s.serial#,
    s.username as 用户名,
    s.program as 程序,
    s.machine as 机器,
    CASE 
        WHEN s.blocking_session IS NOT NULL THEN '被阻塞'
        WHEN EXISTS (SELECT 1 FROM v$session s2 WHERE s2.blocking_session = s.sid) THEN '阻塞源'
        WHEN (SYSDATE - s.logon_time) * 24 > 8 THEN '超长连接(>8小时)'
        WHEN s.last_call_et > 7200 THEN '长时间空闲(>2小时)'
        WHEN s.status = 'KILLED' THEN '已终止但未清理'
        ELSE '其他异常'
    END as 异常类型,
    ROUND((SYSDATE - s.logon_time) * 24, 1) as 连接小时数,
    ROUND(s.last_call_et/60, 1) as 空闲分钟数,
    s.status as 状态
FROM v$session s
WHERE s.username IS NOT NULL
  AND (s.blocking_session IS NOT NULL 
       OR EXISTS (SELECT 1 FROM v$session s2 WHERE s2.blocking_session = s.sid)
       OR (SYSDATE - s.logon_time) * 24 > 8
       OR s.last_call_et > 7200
       OR s.status = 'KILLED')
ORDER BY 
    CASE 
        WHEN s.blocking_session IS NOT NULL THEN 1
        WHEN EXISTS (SELECT 1 FROM v$session s2 WHERE s2.blocking_session = s.sid) THEN 2
        WHEN s.status = 'KILLED' THEN 3
        ELSE 4
    END,
    s.last_call_et DESC;

-- 7. 总体健康评分
-- =====================================================
PROMPT 
PROMPT ========== 数据库健康评分 ==========

WITH health_metrics AS (
    SELECT 
        CASE 
            WHEN (SELECT COUNT(*) FROM v$session) / (SELECT value FROM v$parameter WHERE name = 'sessions') <= 0.7 THEN 100
            WHEN (SELECT COUNT(*) FROM v$session) / (SELECT value FROM v$parameter WHERE name = 'sessions') <= 0.8 THEN 80
            WHEN (SELECT COUNT(*) FROM v$session) / (SELECT value FROM v$parameter WHERE name = 'sessions') <= 0.9 THEN 60
            ELSE 30
        END as 连接评分,
        CASE 
            WHEN (SELECT COUNT(*) FROM v$lock WHERE block = 1) = 0 THEN 100
            WHEN (SELECT COUNT(*) FROM v$lock WHERE block = 1) <= 2 THEN 70
            WHEN (SELECT COUNT(*) FROM v$lock WHERE block = 1) <= 5 THEN 40
            ELSE 20
        END as 锁评分,
        CASE 
            WHEN (SELECT COUNT(*) FROM v$session WHERE blocking_session IS NOT NULL) = 0 THEN 100
            WHEN (SELECT COUNT(*) FROM v$session WHERE blocking_session IS NOT NULL) <= 5 THEN 70
            WHEN (SELECT COUNT(*) FROM v$session WHERE blocking_session IS NOT NULL) <= 10 THEN 40
            ELSE 20
        END as 阻塞评分
    FROM dual
)
SELECT 
    连接评分,
    锁评分,
    阻塞评分,
    ROUND((连接评分 + 锁评分 + 阻塞评分) / 3, 0) as 综合健康评分,
    CASE 
        WHEN ROUND((连接评分 + 锁评分 + 阻塞评分) / 3, 0) >= 90 THEN '优秀'
        WHEN ROUND((连接评分 + 锁评分 + 阻塞评分) / 3, 0) >= 80 THEN '良好'
        WHEN ROUND((连接评分 + 锁评分 + 阻塞评分) / 3, 0) >= 60 THEN '一般'
        WHEN ROUND((连接评分 + 锁评分 + 阻塞评分) / 3, 0) >= 40 THEN '较差'
        ELSE '危险'
    END as 健康等级,
    TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') as 评估时间
FROM health_metrics;

PROMPT 
PROMPT =====================================================
PROMPT 监控报告生成完成
PROMPT =====================================================

-- 恢复设置
SET FEEDBACK ON
