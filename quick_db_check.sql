-- =====================================================
-- Oracle 数据库快速检查脚本
-- 用于日常监控数据库连接数和锁表情况
-- =====================================================

-- 快速连接数检查
-- =====================================================
PROMPT ========== 数据库连接数检查 ==========

SELECT 
    '连接数统计' as 检查项,
    (SELECT COUNT(*) FROM v$session) as 当前总会话数,
    (SELECT COUNT(*) FROM v$session WHERE status = 'ACTIVE') as 活跃会话数,
    (SELECT value FROM v$parameter WHERE name = 'sessions') as 最大会话数限制,
    ROUND((SELECT COUNT(*) FROM v$session) / (SELECT value FROM v$parameter WHERE name = 'sessions') * 100, 1) || '%' as 使用率
FROM dual;

-- 按用户统计连接数（前10名）
SELECT 
    username as 用户名,
    COUNT(*) as 连接数,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as 活跃数
FROM v$session 
WHERE username IS NOT NULL
GROUP BY username
HAVING COUNT(*) > 1
ORDER BY COUNT(*) DESC
FETCH FIRST 10 ROWS ONLY;

-- 快速锁表检查
-- =====================================================
PROMPT ========== 锁表情况检查 ==========

-- 锁统计概览
SELECT 
    '锁情况统计' as 检查项,
    (SELECT COUNT(*) FROM v$lock WHERE type IN ('TM', 'TX')) as 总锁数,
    (SELECT COUNT(*) FROM v$lock WHERE block = 1) as 阻塞锁数,
    (SELECT COUNT(*) FROM v$session WHERE blocking_session IS NOT NULL) as 被阻塞会话数,
    CASE 
        WHEN (SELECT COUNT(*) FROM v$lock WHERE block = 1) > 0 THEN '有阻塞'
        ELSE '无阻塞'
    END as 阻塞状态
FROM dual;

-- 当前阻塞情况（如果存在）
SELECT 
    '阻塞详情' as 类型,
    l.sid as 阻塞会话ID,
    s.username as 阻塞用户,
    s.program as 程序,
    o.object_name as 被锁对象,
    DECODE(l.lmode, 
        2, 'Row-S', 3, 'Row-X', 4, 'Share', 
        5, 'S/Row-X', 6, 'Exclusive', 
        TO_CHAR(l.lmode)) as 锁模式,
    (SELECT COUNT(*) FROM v$session WHERE blocking_session = l.sid) as 阻塞的会话数
FROM v$lock l
JOIN v$session s ON l.sid = s.sid
LEFT JOIN dba_objects o ON l.id1 = o.object_id
WHERE l.block = 1
  AND l.type IN ('TM', 'TX');

-- 被阻塞的会话
SELECT 
    '被阻塞会话' as 类型,
    s.sid as 会话ID,
    s.username as 用户名,
    s.program as 程序,
    s.blocking_session as 阻塞它的会话ID,
    s.seconds_in_wait as 等待秒数,
    s.event as 等待事件
FROM v$session s
WHERE s.blocking_session IS NOT NULL
ORDER BY s.seconds_in_wait DESC;

-- 长时间运行的事务
SELECT 
    '长事务检查' as 类型,
    s.sid,
    s.username as 用户名,
    s.program as 程序,
    ROUND((SYSDATE - t.start_date) * 24 * 60, 1) as 事务运行分钟数,
    t.status as 事务状态
FROM v$session s
JOIN v$transaction t ON s.saddr = t.ses_addr
WHERE (SYSDATE - t.start_date) * 24 * 60 > 30  -- 超过30分钟的事务
ORDER BY t.start_date;

-- 健康状态总结
-- =====================================================
PROMPT ========== 数据库健康状态总结 ==========

SELECT 
    '健康检查总结' as 检查项,
    CASE 
        WHEN (SELECT COUNT(*) FROM v$session) / (SELECT value FROM v$parameter WHERE name = 'sessions') > 0.9 
        THEN '危险: 会话使用率>90%'
        WHEN (SELECT COUNT(*) FROM v$session) / (SELECT value FROM v$parameter WHERE name = 'sessions') > 0.8 
        THEN '警告: 会话使用率>80%'
        ELSE '正常: 会话使用率正常'
    END as 连接状态,
    CASE 
        WHEN (SELECT COUNT(*) FROM v$lock WHERE block = 1) > 5 
        THEN '严重: 阻塞锁>5个'
        WHEN (SELECT COUNT(*) FROM v$lock WHERE block = 1) > 0 
        THEN '警告: 存在阻塞锁'
        ELSE '正常: 无阻塞锁'
    END as 锁状态,
    CASE 
        WHEN (SELECT COUNT(*) FROM v$session WHERE blocking_session IS NOT NULL) > 10 
        THEN '严重: 被阻塞会话>10个'
        WHEN (SELECT COUNT(*) FROM v$session WHERE blocking_session IS NOT NULL) > 0 
        THEN '警告: 存在被阻塞会话'
        ELSE '正常: 无被阻塞会话'
    END as 阻塞状态,
    TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') as 检查时间
FROM dual;

-- 如果需要终止阻塞会话，可以使用以下命令（请谨慎使用）：
-- ALTER SYSTEM KILL SESSION 'sid,serial#';

PROMPT ========== 检查完成 ==========
