-- =====================================================
-- Oracle 数据库连接数和锁表监控脚本
-- =====================================================

-- 1. 查询数据库连接数统计
-- =====================================================

-- 1.1 当前会话总数和活跃会话数
SELECT 
    '当前会话统计' as 统计类型,
    COUNT(*) as 总会话数,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as 活跃会话数,
    COUNT(CASE WHEN status = 'INACTIVE' THEN 1 END) as 非活跃会话数,
    COUNT(CASE WHEN status = 'KILLED' THEN 1 END) as 已终止会话数
FROM v$session;

-- 1.2 按用户分组的会话统计
SELECT 
    username as 用户名,
    COUNT(*) as 会话数,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as 活跃会话数,
    COUNT(CASE WHEN status = 'INACTIVE' THEN 1 END) as 非活跃会话数
FROM v$session 
WHERE username IS NOT NULL
GROUP BY username
ORDER BY COUNT(*) DESC;

-- 1.3 按程序分组的会话统计
SELECT 
    program as 程序名,
    COUNT(*) as 会话数,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as 活跃会话数
FROM v$session 
WHERE program IS NOT NULL
GROUP BY program
ORDER BY COUNT(*) DESC;

-- 1.4 数据库连接限制和当前使用情况
SELECT 
    '连接限制检查' as 检查项,
    (SELECT value FROM v$parameter WHERE name = 'processes') as 最大进程数,
    (SELECT value FROM v$parameter WHERE name = 'sessions') as 最大会话数,
    (SELECT COUNT(*) FROM v$session) as 当前会话数,
    (SELECT COUNT(*) FROM v$process) as 当前进程数,
    ROUND((SELECT COUNT(*) FROM v$session) / (SELECT value FROM v$parameter WHERE name = 'sessions') * 100, 2) as 会话使用率百分比,
    ROUND((SELECT COUNT(*) FROM v$process) / (SELECT value FROM v$parameter WHERE name = 'processes') * 100, 2) as 进程使用率百分比
FROM dual;

-- 1.5 长时间运行的会话
SELECT 
    s.sid,
    s.serial#,
    s.username as 用户名,
    s.program as 程序,
    s.machine as 机器名,
    s.status as 状态,
    ROUND((SYSDATE - s.logon_time) * 24, 2) as 连接时长_小时,
    s.last_call_et as 最后调用秒数
FROM v$session s
WHERE s.username IS NOT NULL
  AND (SYSDATE - s.logon_time) * 24 > 1  -- 连接超过1小时的会话
ORDER BY s.logon_time;

-- =====================================================
-- 2. 锁表检查
-- =====================================================

-- 2.1 当前数据库锁情况总览
SELECT 
    '锁统计' as 统计类型,
    COUNT(*) as 总锁数,
    COUNT(CASE WHEN block = 1 THEN 1 END) as 阻塞锁数,
    COUNT(CASE WHEN lmode > 0 AND request = 0 THEN 1 END) as 持有锁数,
    COUNT(CASE WHEN lmode = 0 AND request > 0 THEN 1 END) as 等待锁数
FROM v$lock;

-- 2.2 详细的锁信息和阻塞会话
SELECT 
    l.sid as 会话ID,
    s.username as 用户名,
    s.program as 程序,
    s.machine as 机器名,
    o.object_name as 对象名,
    o.object_type as 对象类型,
    l.type as 锁类型,
    DECODE(l.lmode, 
        0, 'None',
        1, 'Null', 
        2, 'Row-S (SS)', 
        3, 'Row-X (SX)', 
        4, 'Share', 
        5, 'S/Row-X (SSX)', 
        6, 'Exclusive', 
        TO_CHAR(l.lmode)) as 锁模式,
    DECODE(l.request, 
        0, 'None',
        1, 'Null', 
        2, 'Row-S (SS)', 
        3, 'Row-X (SX)', 
        4, 'Share', 
        5, 'S/Row-X (SSX)', 
        6, 'Exclusive', 
        TO_CHAR(l.request)) as 请求模式,
    l.block as 是否阻塞,
    s.status as 会话状态,
    s.last_call_et as 最后调用秒数
FROM v$lock l
JOIN v$session s ON l.sid = s.sid
LEFT JOIN dba_objects o ON l.id1 = o.object_id
WHERE l.type IN ('TM', 'TX')  -- TM: 表锁, TX: 事务锁
ORDER BY l.block DESC, l.sid;

-- 2.3 阻塞和被阻塞的会话关系
SELECT 
    blocking.sid as 阻塞会话ID,
    blocking.username as 阻塞用户,
    blocking.program as 阻塞程序,
    blocked.sid as 被阻塞会话ID,
    blocked.username as 被阻塞用户,
    blocked.program as 被阻塞程序,
    w.event as 等待事件,
    w.seconds_in_wait as 等待秒数,
    o.object_name as 对象名,
    o.object_type as 对象类型
FROM v$session blocking
JOIN v$session blocked ON blocking.sid = blocked.blocking_session
LEFT JOIN v$session_wait w ON blocked.sid = w.sid
LEFT JOIN dba_objects o ON w.p1 = o.object_id
WHERE blocking.blocking_session IS NULL
  AND blocked.blocking_session IS NOT NULL;

-- 2.4 当前正在执行的SQL和可能的锁等待
SELECT 
    s.sid,
    s.serial#,
    s.username as 用户名,
    s.status as 状态,
    s.event as 当前等待事件,
    s.seconds_in_wait as 等待秒数,
    sq.sql_text as 当前SQL,
    s.blocking_session as 阻塞会话ID
FROM v$session s
LEFT JOIN v$sql sq ON s.sql_id = sq.sql_id
WHERE s.username IS NOT NULL
  AND (s.event LIKE '%lock%' OR s.blocking_session IS NOT NULL OR s.status = 'ACTIVE')
ORDER BY s.seconds_in_wait DESC;

-- 2.5 表级锁的详细信息
SELECT 
    s.sid,
    s.username as 用户名,
    o.owner as 对象所有者,
    o.object_name as 表名,
    DECODE(l.lmode,
        0, 'None',
        1, 'Null',
        2, 'Row-S (SS)',
        3, 'Row-X (SX)',
        4, 'Share',
        5, 'S/Row-X (SSX)',
        6, 'Exclusive',
        TO_CHAR(l.lmode)) as 锁模式,
    DECODE(l.request,
        0, 'None',
        1, 'Null',
        2, 'Row-S (SS)',
        3, 'Row-X (SX)',
        4, 'Share',
        5, 'S/Row-X (SSX)',
        6, 'Exclusive',
        TO_CHAR(l.request)) as 请求模式,
    l.block as 是否阻塞其他会话
FROM v$lock l
JOIN v$session s ON l.sid = s.sid
JOIN dba_objects o ON l.id1 = o.object_id
WHERE l.type = 'TM'  -- 表锁
  AND s.username IS NOT NULL
ORDER BY o.object_name, s.sid;

-- =====================================================
-- 3. 快速诊断脚本
-- =====================================================

-- 3.1 一键检查数据库健康状态
SELECT 
    '数据库健康检查' as 检查项,
    CASE 
        WHEN (SELECT COUNT(*) FROM v$session) / (SELECT value FROM v$parameter WHERE name = 'sessions') > 0.8 
        THEN '警告: 会话使用率超过80%'
        ELSE '正常: 会话使用率正常'
    END as 会话状态,
    CASE 
        WHEN (SELECT COUNT(*) FROM v$lock WHERE block = 1) > 0 
        THEN '警告: 存在阻塞锁'
        ELSE '正常: 无阻塞锁'
    END as 锁状态,
    CASE 
        WHEN (SELECT COUNT(*) FROM v$session WHERE blocking_session IS NOT NULL) > 0 
        THEN '警告: 存在被阻塞会话'
        ELSE '正常: 无被阻塞会话'
    END as 阻塞状态
FROM dual;

-- 3.2 需要关注的会话（长时间运行或阻塞）
SELECT 
    s.sid,
    s.serial#,
    s.username as 用户名,
    s.program as 程序,
    CASE 
        WHEN s.blocking_session IS NOT NULL THEN '被阻塞会话'
        WHEN EXISTS (SELECT 1 FROM v$session s2 WHERE s2.blocking_session = s.sid) THEN '阻塞会话'
        WHEN (SYSDATE - s.logon_time) * 24 > 2 THEN '长时间连接'
        WHEN s.last_call_et > 3600 THEN '长时间无活动'
        ELSE '正常会话'
    END as 会话类型,
    ROUND((SYSDATE - s.logon_time) * 24, 2) as 连接时长_小时,
    s.last_call_et as 最后调用秒数,
    s.status as 状态
FROM v$session s
WHERE s.username IS NOT NULL
  AND (s.blocking_session IS NOT NULL 
       OR EXISTS (SELECT 1 FROM v$session s2 WHERE s2.blocking_session = s.sid)
       OR (SYSDATE - s.logon_time) * 24 > 2
       OR s.last_call_et > 3600)
ORDER BY 
    CASE 
        WHEN s.blocking_session IS NOT NULL THEN 1
        WHEN EXISTS (SELECT 1 FROM v$session s2 WHERE s2.blocking_session = s.sid) THEN 2
        ELSE 3
    END,
    s.last_call_et DESC;
